{"name": "base", "version": "0.1.0", "private": true, "scripts": {"dev": "run-p next:dev sanity:typegen:watch", "next:dev": "next dev --turbo", "build": "run-s sanity:typegen next:build", "next:build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write --ignore-unknown .", "typecheck": "tsc --noEmit", "sanity:typegen:watch": "watch \"npm run sanity:typegen\" ./sanity ./data", "sanity:typegen": "sanity schema extract --path ./types/schema.json && sanity typegen generate"}, "dependencies": {"@medusajs/js-sdk": "2.0.0", "@medusajs/types": "2.0.0", "@next/env": "15.0.0", "@portabletext/react": "^3.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-visually-hidden": "^1.1.0", "@sanity/asset-utils": "^1.3.0", "@sanity/icons": "^3.2.0", "@sanity/image-url": "^1.0.2", "@sanity/orderable-document-list": "^1.2.1", "@sanity/ui": "^2.6.1", "@sanity/vision": "^3.48.1", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.8.0", "@tinloof/sanity-studio": "^1.4.0", "@tinloof/sanity-web": "^0.6.0", "@vercel/analytics": "^1.3.1", "class-variance-authority": "^0.7.0", "cva": "npm:class-variance-authority@^0.7.0", "embla-carousel": "^8.3.0", "embla-carousel-react": "^8.3.0", "lodash": "^4.17.21", "next": "15.0.0", "next-sanity": "^9.7.1", "nuqs": "^1.20.0", "react": "19.0.0-rc-65a56d0e-20241020", "react-dom": "19.0.0-rc-65a56d0e-20241020", "react-icons": "^5.3.0", "react-remove-scroll": "^2.6.0", "sanity": "^3.62.0", "sanity-plugin-hotspot-array": "^2.0.0", "zod": "^3.23.8"}, "devDependencies": {"@medusajs/client-types": "preview", "@portabletext/types": "^2.0.13", "@types/lodash": "^4.17.6", "@types/node": "^20.14.9", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "15.0.0", "eslint-import-resolver-node": "^0.3.9", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-perfectionist": "^2.11.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.39", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "typescript-eslint": "^7.15.0", "watch": "^1.0.2"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}, "packageManager": "pnpm@9.12.2+sha512.22721b3a11f81661ae1ec68ce1a7b879425a1ca5b991c975b074ac220b187ce56c708fe5db69f4c962c989452eee76c82877f4ee80f474cebd61ee13461b6228"}