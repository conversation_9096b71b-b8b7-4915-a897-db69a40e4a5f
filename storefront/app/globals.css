@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html body[data-scroll-locked] {
    overflow: visible !important;
  }
  body {
    color: var(--text-primary);
    background: var(--background);
    @apply font-serif;
  }
  :root {
    @apply [--header-height:_89px] lg:[--header-height:_108px];
    --max-width: 1440px;
    --min-width: 320px;
    /* Japanese Minimalist Colors */
    --background: #ffffff;
    --background-warm: #fff6e6;
    --accent: #ff5227;
    --accent-40: rgba(255, 82, 39, 0.4);
    --accent-20: rgba(255, 82, 39, 0.2);
    --accent-10: rgba(255, 82, 39, 0.1);
    --accent-05: rgba(255, 82, 39, 0.05);
    --secondary: #f9dff2;
    /* Text Colors for Japanese Minimalism */
    --text-primary: rgba(255, 82, 39, 0.9);
    --text-secondary: rgba(255, 82, 39, 0.7);
    --text-tertiary: rgba(255, 82, 39, 0.5);
    --text-muted: rgba(255, 82, 39, 0.3);
    --base-font-size: 16px;
  }
  .font-serif {
    --base-font-size: 16px;
  }
  .font-display {
    --base-font-size: 16px;
  }

  .hero-asset {
    min-height: min(590px, calc(100vh - var(--header-height) - 1rem));
  }

  .newletter-text:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
    box-shadow: 0 0 0px 1000px transparent inset !important;
    -webkit-text-fill-color: #ff5227 !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }

  ::-webkit-scrollbar {
    width: auto;
  }

  ::-webkit-scrollbar-track {
    background: #ff52273c;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #ff5227;
    border-radius: 10px;
  }
  * {
    scrollbar-width: thin;
    scrollbar-color: #ff5227 #ff52273c;
  }
  @layer utilities {
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }
}
