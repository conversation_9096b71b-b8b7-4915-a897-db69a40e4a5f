import Heading from "@/components/shared/typography/heading"
import Body from "@/components/shared/typography/body"

export default function HeroSection() {
  return (
    <section className="relative min-h-[70vh] flex items-center justify-center bg-gradient-to-br from-amber-50 to-orange-50 overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-accent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-secondary rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-accent-40 rounded-full blur-2xl"></div>
      </div>
      
      {/* Main content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-8">
          <Heading
            tag="h1"
            font="serif"
            mobileSize="3xl"
            desktopSize="7xl"
            className="text-accent mb-6 max-w-4xl mx-auto"
          >
            Cokugoo: Where Wood Carving Art Finds Its Soul
          </Heading>
          
          <Body
            font="serif"
            mobileSize="lg"
            desktopSize="xl"
            className="text-accent/80 max-w-4xl mx-auto leading-relaxed"
          >
            In a world of fleeting digital noise, we find truth in the tangible grain of wood. At cokugoo, we don't just craft objects; we channel the spirit of the wild into timeless pieces through the cherished tradition of wood carving. Each of our creations is a bridge between the heart of the forest and the soul of your home.
          </Body>
        </div>
        
        {/* Featured image placeholder */}
        <div className="mt-12 relative">
          <div className="aspect-[16/9] max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-amber-100 to-orange-100 border border-accent/10">
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-amber-50 to-orange-50">
              <div className="text-center p-8">
                <div className="w-24 h-24 mx-auto mb-4 bg-accent/20 rounded-full flex items-center justify-center">
                  <svg className="w-12 h-12 text-accent" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                  </svg>
                </div>
                <Body font="serif" mobileSize="sm" className="text-accent/60">
                  A stunning collection of hand carved wooden animals arranged artfully on natural oak, showcasing elegant wooden animal decor in a warm, minimalist setting
                </Body>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
