import Heading from "@/components/shared/typography/heading"
import Body from "@/components/shared/typography/body"

export default function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-background overflow-hidden">
      {/* Minimalist background pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, var(--accent-05) 1px, transparent 1px),
                           radial-gradient(circle at 75% 75%, var(--accent-05) 1px, transparent 1px)`,
          backgroundSize: '80px 80px'
        }}></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 max-w-5xl mx-auto px-6 lg:px-8">
        <div className="text-center space-y-12">
          {/* Zen-inspired divider */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="w-12 h-px bg-text-light"></div>
            <div className="w-2 h-2 bg-text-muted rounded-full"></div>
            <div className="w-12 h-px bg-text-light"></div>
          </div>

          <Heading
            className="text-text-primary leading-tight tracking-tight max-w-4xl mx-auto"
            desktopSize="6xl"
            font="serif"
            mobileSize="3xl"
            tag="h1"
          >
            Cokugoo: Where Wood Carving Art Finds Its Soul
          </Heading>

          <div className="max-w-3xl mx-auto">
            <Body
              className="text-text-secondary leading-relaxed"
              desktopSize="xl"
              font="serif"
              mobileSize="lg"
            >
              In a world of fleeting digital noise, we find truth in the tangible grain of wood. At cokugoo, we don't just craft objects; we channel the spirit of the wild into timeless pieces through the cherished tradition of wood carving. Each of our creations is a bridge between the heart of the forest and the soul of your home.
            </Body>
          </div>

          {/* Featured image with Japanese aesthetic */}
          <div className="mt-16 relative">
            <div className="aspect-[4/3] max-w-3xl mx-auto bg-background-warm rounded-lg border border-accent-05 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center p-12">
                <div className="text-center space-y-6">
                  {/* Minimalist wood grain icon */}
                  <div className="w-16 h-16 mx-auto">
                    <svg viewBox="0 0 64 64" className="w-full h-full text-text-muted" fill="currentColor">
                      <path d="M8 16c0-4.4 3.6-8 8-8h32c4.4 0 8 3.6 8 8v32c0 4.4-3.6 8-8 8H16c-4.4 0-8-3.6-8-8V16z" opacity="0.1"/>
                      <path d="M12 20h40M12 28h40M12 36h40M12 44h40" stroke="currentColor" strokeWidth="1" fill="none"/>
                    </svg>
                  </div>
                  <Body className="text-text-tertiary max-w-md mx-auto" desktopSize="base" font="serif" mobileSize="sm">
                    A stunning collection of hand carved wooden animals arranged artfully on natural oak, showcasing elegant wooden animal decor in a warm, minimalist setting
                  </Body>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom zen divider */}
          <div className="flex items-center justify-center space-x-4 mt-16">
            <div className="w-8 h-px bg-text-light"></div>
            <div className="w-1 h-1 bg-text-muted rounded-full"></div>
            <div className="w-16 h-px bg-text-light"></div>
            <div className="w-1 h-1 bg-text-muted rounded-full"></div>
            <div className="w-8 h-px bg-text-light"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
