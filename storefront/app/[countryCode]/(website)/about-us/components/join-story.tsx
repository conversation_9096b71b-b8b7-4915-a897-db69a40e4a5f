import Heading from "@/components/shared/typography/heading"
import Body from "@/components/shared/typography/body"
import { Link } from "@/components/shared/button"

export default function JoinStorySection() {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-amber-50 to-orange-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 right-10 w-32 h-32 bg-accent rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 left-10 w-40 h-40 bg-secondary rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-accent-40 rounded-full blur-2xl animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Section header */}
        <div className="mb-12">
          <Heading
            tag="h2"
            font="serif"
            mobileSize="2xl"
            desktopSize="5xl"
            className="text-accent mb-8"
          >
            Join the Cokugoo Story
          </Heading>
          
          <Body
            font="serif"
            mobileSize="lg"
            desktopSize="xl"
            className="text-accent/80 leading-relaxed max-w-2xl mx-auto"
          >
            We invite you to bring a piece of this narrative into your home. Discover a creation that speaks to you and experience the quiet joy of our craft.
          </Body>
        </div>

        {/* CTA Section */}
        <div className="relative">
          {/* Background card */}
          <div className="bg-white/70 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-accent/10 shadow-xl">
            {/* Decorative icon */}
            <div className="mb-8">
              <div className="w-20 h-20 mx-auto bg-accent/10 rounded-full flex items-center justify-center">
                <svg className="w-10 h-10 text-accent" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                </svg>
              </div>
            </div>

            {/* CTA Button */}
            <div className="mb-6">
              <Link
                href="/collections"
                size="xl"
                variant="primary"
                className="inline-flex items-center space-x-2 hover:scale-105 transition-transform duration-300"
              >
                <span>Explore Our Collection of Wooden Animal Figurines</span>
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* Additional info */}
            <Body
              font="serif"
              mobileSize="sm"
              desktopSize="base"
              className="text-accent/60"
            >
              Each piece is handcrafted with love and comes with its own unique story
            </Body>
          </div>

          {/* Floating decorative elements */}
          <div className="absolute -top-4 -left-4 w-8 h-8 bg-accent/20 rounded-full animate-bounce"></div>
          <div className="absolute -top-2 -right-6 w-6 h-6 bg-secondary/30 rounded-full animate-bounce" style={{animationDelay: '0.5s'}}></div>
          <div className="absolute -bottom-3 -left-2 w-4 h-4 bg-accent-40 rounded-full animate-bounce" style={{animationDelay: '1s'}}></div>
          <div className="absolute -bottom-4 -right-4 w-5 h-5 bg-accent/30 rounded-full animate-bounce" style={{animationDelay: '1.5s'}}></div>
        </div>

        {/* Bottom decorative line */}
        <div className="mt-16 flex justify-center">
          <div className="flex space-x-2">
            <div className="w-12 h-0.5 bg-accent/30 rounded-full"></div>
            <div className="w-8 h-0.5 bg-accent/50 rounded-full"></div>
            <div className="w-4 h-0.5 bg-accent/70 rounded-full"></div>
            <div className="w-2 h-0.5 bg-accent rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
