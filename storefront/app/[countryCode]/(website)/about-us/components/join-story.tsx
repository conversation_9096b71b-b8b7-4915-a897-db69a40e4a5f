import Body from "@/components/shared/typography/body"
import Heading from "@/components/shared/typography/heading"
import { Link } from "@/components/shared/button"

export default function JoinStorySection() {
  return (
    <section className="py-20 lg:py-32 bg-background relative">
      <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
        {/* Zen-inspired section header */}
        <div className="mb-16">
          <div className="flex items-center justify-center mb-8">
            <div className="w-32 h-px bg-text-muted"></div>
            <div className="mx-8 text-2xl text-text-muted font-light">◦</div>
            <div className="w-32 h-px bg-text-muted"></div>
          </div>

          <Heading
            className="text-text-primary mb-8 tracking-tight"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            Join the Cokugoo Story
          </Heading>

          <Body
            className="text-text-secondary leading-relaxed max-w-2xl mx-auto"
            desktopSize="xl"
            font="serif"
            mobileSize="lg"
          >
            We invite you to bring a piece of this narrative into your home. Discover a creation that speaks to you and experience the quiet joy of our craft.
          </Body>
        </div>

        {/* Minimalist CTA section */}
        <div className="space-y-12">
          {/* Clean CTA card */}
          <div className="bg-white rounded-2xl p-12 lg:p-16 border border-accent-05 shadow-sm">
            {/* Subtle wood grain symbol */}
            <div className="mb-8">
              <div className="w-16 h-16 mx-auto border border-text-muted rounded-lg flex items-center justify-center">
                <svg viewBox="0 0 32 32" className="w-8 h-8 text-text-tertiary" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path d="M4 8h24M4 16h24M4 24h24" strokeLinecap="round"/>
                  <path d="M8 4v24M16 4v24M24 4v24" strokeLinecap="round" opacity="0.3"/>
                </svg>
              </div>
            </div>

            {/* CTA Button with minimal styling */}
            <div className="mb-8">
              <Link
                className="inline-flex items-center space-x-3 hover:scale-[1.02] transition-transform duration-300"
                href="/collections"
                size="xl"
                variant="primary"
              >
                <span>Explore Our Collection of Wooden Animal Figurines</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* Subtle additional info */}
            <Body
              className="text-text-muted"
              desktopSize="base"
              font="serif"
              mobileSize="sm"
            >
              Each piece is handcrafted with love and comes with its own unique story
            </Body>
          </div>

          {/* Final zen elements */}
          <div className="flex justify-center">
            <div className="flex items-center space-x-4">
              <div className="w-6 h-px bg-text-muted"></div>
              <div className="w-1 h-1 bg-text-tertiary rounded-full"></div>
              <div className="w-12 h-px bg-text-muted"></div>
              <div className="w-1 h-1 bg-text-tertiary rounded-full"></div>
              <div className="w-6 h-px bg-text-muted"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
