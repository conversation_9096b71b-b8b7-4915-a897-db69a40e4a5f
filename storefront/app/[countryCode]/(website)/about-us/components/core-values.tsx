import Body from "@/components/shared/typography/body"
import Heading from "@/components/shared/typography/heading"

const values = [
  {
    title: "Artisan Soul",
    description: "We honor the slow, meditative process of traditional wood carving. Every single one of our hand carved wooden animals is shaped by human hands, ensuring that no two are ever identical. This is where true skill meets artistic spirit.",
    symbol: "手"
  },
  {
    title: "Natural Harmony",
    description: "True beauty respects its source. We are deeply committed to using only ethically sourced, sustainable wood. This ensures our wooden animal decor is not only safe for your home but also kind to the planet that inspires us.",
    symbol: "木"
  },
  {
    title: "Enduring Charm",
    description: "We create more than decorations; we create companions. Our wooden animal figurines are designed to transcend trends, becoming cherished heirlooms that carry stories and warmth through generations.",
    symbol: "心"
  }
]

export default function CoreValues() {
  return (
    <section className="py-20 lg:py-32 bg-white">
      <div className="max-w-6xl mx-auto px-6 lg:px-8">
        {/* Zen-inspired section header */}
        <div className="text-center mb-20">
          <div className="flex items-center justify-center mb-8">
            <div className="w-16 h-px bg-accent/20"></div>
            <div className="mx-4 w-3 h-3 border border-accent/30 rotate-45"></div>
            <div className="w-16 h-px bg-accent/20"></div>
          </div>

          <Heading
            className="text-accent mb-8 tracking-tight"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            Our Core Values: The Principles of Our Craft
          </Heading>

          <Body
            className="text-accent/70 max-w-2xl mx-auto leading-relaxed"
            desktopSize="xl"
            font="serif"
            mobileSize="lg"
          >
            Our brand is guided by three principles. They are the soul breathed into every piece of wood carving art we create:
          </Body>
        </div>

        {/* Minimalist values layout */}
        <div className="space-y-16 lg:space-y-24">
          {values.map((value, index) => (
            <div
              key={index}
              className={`flex flex-col lg:flex-row items-center gap-12 lg:gap-16 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Japanese character symbol */}
              <div className="flex-shrink-0">
                <div className="w-32 h-32 lg:w-40 lg:h-40 bg-white border border-accent/10 rounded-lg flex items-center justify-center shadow-sm">
                  <span className="text-6xl lg:text-7xl text-accent/80 font-light">
                    {value.symbol}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 text-center lg:text-left space-y-6">
                <Heading
                  className="text-accent tracking-tight"
                  desktopSize="3xl"
                  font="serif"
                  mobileSize="xl"
                  tag="h3"
                >
                  {value.title}
                </Heading>

                <Body
                  className="text-accent/70 leading-relaxed max-w-lg mx-auto lg:mx-0"
                  desktopSize="lg"
                  font="serif"
                  mobileSize="base"
                >
                  {value.description}
                </Body>

                {/* Subtle accent line */}
                <div className="flex justify-center lg:justify-start">
                  <div className="w-12 h-px bg-accent/30"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
