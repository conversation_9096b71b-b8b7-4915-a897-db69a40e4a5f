import Heading from "@/components/shared/typography/heading"
import Body from "@/components/shared/typography/body"

const values = [
  {
    title: "Artisan Soul",
    description: "We honor the slow, meditative process of traditional wood carving. Every single one of our hand carved wooden animals is shaped by human hands, ensuring that no two are ever identical. This is where true skill meets artistic spirit.",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    )
  },
  {
    title: "Natural Harmony",
    description: "True beauty respects its source. We are deeply committed to using only ethically sourced, sustainable wood. This ensures our wooden animal decor is not only safe for your home but also kind to the planet that inspires us.",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M17 8C8 10 5.9 16.17 3.82 21.34l1.89.66C7.84 17.17 9.64 12.65 17 11V8zm-5.55 2.83c-2.61.61-4.31 2.84-4.31 5.17 0 3.31 2.69 6 6 6s6-2.69 6-6c0-2.33-1.7-4.56-4.31-5.17-.4-.09-.69-.4-.69-.83 0-.55.45-1 1-1s1 .45 1 1c0 .*********.5s.5-.22.5-.5c0-1.1-.9-2-2-2s-2 .9-2 2c0 .43-.29.74-.69.83z"/>
      </svg>
    )
  },
  {
    title: "Enduring Charm",
    description: "We create more than decorations; we create companions. Our wooden animal figurines are designed to transcend trends, becoming cherished heirlooms that carry stories and warmth through generations.",
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
      </svg>
    )
  }
]

export default function CoreValues() {
  return (
    <section className="py-16 lg:py-24 bg-background">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <Heading
            tag="h2"
            font="serif"
            mobileSize="2xl"
            desktopSize="5xl"
            className="text-accent mb-6"
          >
            Our Core Values: The Principles of Our Craft
          </Heading>
          <Body
            font="serif"
            mobileSize="lg"
            desktopSize="xl"
            className="text-accent/80 max-w-3xl mx-auto"
          >
            Our brand is guided by three principles. They are the soul breathed into every piece of wood carving art we create:
          </Body>
        </div>

        {/* Values grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {values.map((value, index) => (
            <div
              key={index}
              className="group relative bg-white/50 backdrop-blur-sm rounded-2xl p-8 border border-accent/10 hover:border-accent/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
            >
              {/* Icon */}
              <div className="mb-6">
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center text-accent group-hover:bg-accent/20 transition-colors duration-300">
                  {value.icon}
                </div>
              </div>

              {/* Content */}
              <div>
                <Heading
                  tag="h3"
                  font="serif"
                  mobileSize="xl"
                  desktopSize="2xl"
                  className="text-accent mb-4"
                >
                  {value.title}
                </Heading>
                <Body
                  font="serif"
                  mobileSize="base"
                  desktopSize="lg"
                  className="text-accent/70 leading-relaxed"
                >
                  {value.description}
                </Body>
              </div>

              {/* Decorative element */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-accent/20 rounded-full group-hover:bg-accent/40 transition-colors duration-300"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
