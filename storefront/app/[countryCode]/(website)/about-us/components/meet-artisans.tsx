import Heading from "@/components/shared/typography/heading"
import Body from "@/components/shared/typography/body"

const artisans = [
  {
    name: "Time",
    description: "An elderly master woodcarver with wise, kind eyes and decades of experience. Time brings traditional techniques and deep wisdom to every piece, specializing in classic wooden dragons and intricate details.",
    specialty: "Traditional Techniques & Classic Dragons",
    image: "/images/artisan-time-placeholder.jpg"
  },
  {
    name: "<PERSON>",
    description: "A young female designer with a focused, calm expression. <PERSON> works in a bright, modern workshop, creating sleek, abstract wooden bird figurines with smooth curves and contemporary aesthetics.",
    specialty: "Modern Design & Abstract Birds",
    image: "/images/artisan-riley-placeholder.jpg"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    description: "A ruggedly handsome male artisan in his mid-30s with a gentle beard. <PERSON><PERSON><PERSON> connects deeply with nature, crafting lifelike wooden foxes and forest animals from his rustic, cabin-like workshop.",
    specialty: "Lifelike Forest Animals",
    image: "/images/artisan-arlo-placeholder.jpg"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    description: "A creative and energetic designer with a spark of mischief in his eyes. JIOFA brings whimsy and imagination to life, creating unique wooden monster figurines that are both charming and distinctive.",
    specialty: "Whimsical Creatures & Unique Monsters",
    image: "/images/artisan-jiofa-placeholder.jpg"
  }
]

export default function MeetArtisans() {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-orange-50 to-amber-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <Heading
            tag="h2"
            font="serif"
            mobileSize="2xl"
            desktopSize="5xl"
            className="text-accent mb-6"
          >
            Meet Our Artisans: The Hands Behind the Art
          </Heading>
          <Body
            font="serif"
            mobileSize="lg"
            desktopSize="xl"
            className="text-accent/80 max-w-3xl mx-auto"
          >
            The magic of cokugoo lies in the hearts and hands of our dedicated designers. Each artisan brings a unique perspective to our collective passion for wood carving art.
          </Body>
        </div>

        {/* Artisans grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {artisans.map((artisan, index) => (
            <div
              key={index}
              className="group bg-white/70 backdrop-blur-sm rounded-2xl overflow-hidden border border-accent/10 hover:border-accent/20 transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
            >
              {/* Image placeholder */}
              <div className="aspect-[3/4] bg-gradient-to-br from-amber-100 to-orange-100 relative overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center p-6">
                    <div className="w-20 h-20 mx-auto mb-4 bg-accent/20 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-accent" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                    <Body font="serif" mobileSize="xs" className="text-accent/60">
                      {artisan.name} - Master Artisan
                    </Body>
                  </div>
                </div>
                {/* Decorative overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-accent/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Content */}
              <div className="p-6">
                <Heading
                  tag="h3"
                  font="serif"
                  mobileSize="lg"
                  desktopSize="xl"
                  className="text-accent mb-2"
                >
                  {artisan.name}
                </Heading>
                
                <Body
                  font="serif"
                  mobileSize="sm"
                  desktopSize="base"
                  className="text-accent/60 mb-3 font-medium"
                >
                  {artisan.specialty}
                </Body>
                
                <Body
                  font="serif"
                  mobileSize="sm"
                  desktopSize="base"
                  className="text-accent/70 leading-relaxed"
                >
                  {artisan.description}
                </Body>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
