import Body from "@/components/shared/typography/body"
import Heading from "@/components/shared/typography/heading"

const artisans = [
  {
    name: "Time",
    description: "An elderly master woodcarver with wise, kind eyes and decades of experience. Time brings traditional techniques and deep wisdom to every piece, specializing in classic wooden dragons and intricate details.",
    specialty: "Traditional Techniques & Classic Dragons",
    kanji: "時"
  },
  {
    name: "<PERSON>",
    description: "A young female designer with a focused, calm expression. <PERSON> works in a bright, modern workshop, creating sleek, abstract wooden bird figurines with smooth curves and contemporary aesthetics.",
    specialty: "Modern Design & Abstract Birds",
    kanji: "鳥"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    description: "A ruggedly handsome male artisan in his mid-30s with a gentle beard. <PERSON><PERSON><PERSON> connects deeply with nature, crafting lifelike wooden foxes and forest animals from his rustic, cabin-like workshop.",
    specialty: "Lifelike Forest Animals",
    kanji: "森"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    description: "A creative and energetic designer with a spark of mischief in his eyes. JIOFA brings whimsy and imagination to life, creating unique wooden monster figurines that are both charming and distinctive.",
    specialty: "Whimsical Creatures & Unique Monsters",
    kanji: "創"
  }
]

export default function MeetArtisans() {
  return (
    <section className="py-20 lg:py-32 bg-background-warm">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Zen-inspired section header */}
        <div className="text-center mb-20">
          <div className="flex items-center justify-center mb-8">
            <div className="w-20 h-px bg-text-muted"></div>
            <div className="mx-6 flex space-x-2">
              <div className="w-2 h-2 bg-text-tertiary rounded-full"></div>
              <div className="w-2 h-2 bg-text-muted rounded-full"></div>
              <div className="w-2 h-2 bg-text-tertiary rounded-full"></div>
            </div>
            <div className="w-20 h-px bg-text-muted"></div>
          </div>

          <Heading
            className="text-text-primary mb-8 tracking-tight"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            Meet Our Artisans: The Hands Behind the Art
          </Heading>

          <Body
            className="text-text-secondary max-w-2xl mx-auto leading-relaxed"
            desktopSize="xl"
            font="serif"
            mobileSize="lg"
          >
            The magic of cokugoo lies in the hearts and hands of our dedicated designers. Each artisan brings a unique perspective to our collective passion for wood carving art.
          </Body>
        </div>

        {/* Minimalist artisans grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 lg:gap-16">
          {artisans.map((artisan, index) => (
            <div
              key={index}
              className="group flex flex-col items-center text-center space-y-8 p-8 bg-white rounded-lg border border-accent-05 hover:border-accent-10 transition-all duration-500 hover:shadow-sm"
            >
              {/* Minimalist portrait placeholder */}
              <div className="relative">
                <div className="w-32 h-32 bg-white border-2 border-accent-10 rounded-full flex items-center justify-center shadow-sm group-hover:border-accent-20 transition-colors duration-300">
                  <span className="text-4xl text-text-primary font-light">
                    {artisan.kanji}
                  </span>
                </div>
                {/* Subtle accent ring */}
                <div className="absolute inset-0 rounded-full border border-accent-05 scale-110"></div>
              </div>

              {/* Content */}
              <div className="space-y-4">
                <Heading
                  className="text-text-primary tracking-tight"
                  desktopSize="2xl"
                  font="serif"
                  mobileSize="xl"
                  tag="h3"
                >
                  {artisan.name}
                </Heading>

                <Body
                  className="text-text-tertiary font-medium"
                  desktopSize="base"
                  font="serif"
                  mobileSize="sm"
                >
                  {artisan.specialty}
                </Body>

                <Body
                  className="text-text-secondary leading-relaxed max-w-sm mx-auto"
                  desktopSize="base"
                  font="serif"
                  mobileSize="sm"
                >
                  {artisan.description}
                </Body>

                {/* Zen accent line */}
                <div className="pt-4">
                  <div className="w-8 h-px bg-text-muted mx-auto"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
