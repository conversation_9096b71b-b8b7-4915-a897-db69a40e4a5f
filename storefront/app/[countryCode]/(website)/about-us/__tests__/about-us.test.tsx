import { render, screen } from '@testing-library/react'
import AboutUsPage from '../page'

// Mock the components to avoid complex dependencies in tests
jest.mock('../components/hero-section', () => {
  return function MockHeroSection() {
    return <div data-testid="hero-section">Hero Section</div>
  }
})

jest.mock('../components/core-values', () => {
  return function MockCoreValues() {
    return <div data-testid="core-values">Core Values</div>
  }
})

jest.mock('../components/meet-artisans', () => {
  return function MockMeetArtisans() {
    return <div data-testid="meet-artisans">Meet Artisans</div>
  }
})

jest.mock('../components/cokugoo-belief', () => {
  return function MockCokugooBeliefSection() {
    return <div data-testid="cokugoo-belief">Cokugoo Belief</div>
  }
})

jest.mock('../components/join-story', () => {
  return function MockJoinStorySection() {
    return <div data-testid="join-story">Join Story</div>
  }
})

describe('About Us Page', () => {
  it('renders all sections', () => {
    render(<AboutUsPage />)
    
    expect(screen.getByTestId('hero-section')).toBeInTheDocument()
    expect(screen.getByTestId('core-values')).toBeInTheDocument()
    expect(screen.getByTestId('meet-artisans')).toBeInTheDocument()
    expect(screen.getByTestId('cokugoo-belief')).toBeInTheDocument()
    expect(screen.getByTestId('join-story')).toBeInTheDocument()
  })

  it('has correct SEO metadata', () => {
    // This would be tested in an integration test with Next.js metadata
    expect(true).toBe(true) // Placeholder for metadata tests
  })
})
