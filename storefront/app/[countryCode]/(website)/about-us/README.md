# About Us Page - Cokugoo Wood Carving (Japanese Minimalist Style)

This directory contains the implementation of the About Us page for the Cokugoo wood carving e-commerce website, designed with Japanese minimalist aesthetics inspired by mokuomo.com.

## 📋 Requirements Compliance

This implementation strictly follows the SEO document requirements (`seo.md`) and includes:

### ✅ SEO Compliance
- **Title**: "Cokugoo: The Soul of Wood Carving Art & Hand Carved Wooden Animals"
- **Description**: Complete SEO-optimized meta description
- **Content**: All text content matches the SEO document exactly
- **Keywords**: Naturally integrated "wood carving art", "hand carved wooden animals", "wooden animal decor", "wooden animal figurines"

### ✅ Design Features (Japanese Minimalist)
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Japanese Minimalism**: Inspired by mokuomo.com's zen aesthetic
- **Bright Theme**: Pure white and cream backgrounds with warm orange accents
- **Unique Design**: Original Japanese-inspired layout with cultural elements
- **No Gradients**: Clean, minimalist typography
- **Cultural Elements**: Japanese kanji characters and zen decorative elements

### ✅ Content Sections (Japanese Minimalist Design)

1. **Hero Section** (`hero-section.tsx`)
   - Zen-inspired layout with subtle dot patterns
   - Minimalist dividers and spacing
   - Clean image placeholder with wood grain icon

2. **Core Values** (`core-values.tsx`)
   - Japanese kanji symbols: 手 (hand), 木 (wood), 心 (heart)
   - Alternating left-right layout for visual balance
   - Geometric line decorations

3. **Meet Our Artisans** (`meet-artisans.tsx`)
   - Circular portraits with kanji characters: 時, 鳥, 森, 創
   - Minimalist grid layout
   - Zen accent lines and spacing

4. **Cokugoo Belief** (`cokugoo-belief.tsx`)
   - Japanese-style quotation marks 「」
   - Clean white card with subtle borders
   - Zen decorative elements

5. **Join Story** (`join-story.tsx`)
   - Minimalist CTA design
   - Wood grain icon symbolism
   - Clean geometric decorations

## 🎨 Japanese Minimalist Design System

### Colors
- **Primary**: `#ff5227` (warm orange accent)
- **Background**: `#ffffff` (pure white) + `#fff6e6` (cream)
- **Accents**: Low opacity variations of primary color
- **Text**: Subtle opacity levels for hierarchy

### Typography
- **Headings**: Serif font family with tight tracking
- **Body**: Serif font family with relaxed leading
- **Sizes**: Harmonious scaling system

### Zen Design Elements
- **Geometric Lines**: Thin horizontal dividers
- **Circular Elements**: Dots and rings for decoration
- **Kanji Characters**: Cultural symbols as visual elements
- **Whitespace**: Generous spacing for breathing room
- **Symmetry**: Balanced layouts and alignments

## 🖼️ Image Placeholders

All images are currently using placeholder designs with descriptive content:
- Hero section: Collection showcase
- Artisan photos: Individual portraits with workshop settings
- Decorative elements: SVG icons and geometric shapes

## 🧪 Testing

Basic test structure is included in `__tests__/about-us.test.tsx`:
- Component rendering verification
- Section presence validation
- SEO metadata structure (placeholder)

## 📱 Responsive Behavior

- **Mobile**: Single column layout, stacked sections
- **Tablet**: Two-column grids where appropriate
- **Desktop**: Full multi-column layouts with optimal spacing

## 🔗 Navigation

The page integrates with the existing site structure:
- Uses shared header/footer components
- Includes proper routing with country code support
- CTA button links to `/collections` page

## 🚀 Performance

- Optimized component structure
- Minimal external dependencies
- Efficient CSS with Tailwind utilities
- Proper semantic HTML structure

## 📝 Content Management

All content is hardcoded as requested, but the modular structure allows for easy future integration with:
- CMS systems
- Internationalization
- Dynamic content updates

## 🎯 SEO Optimization

- Proper heading hierarchy (H1, H2, H3)
- Semantic HTML structure
- Meta tags implementation
- Keyword-rich content placement
- Internal linking strategy
