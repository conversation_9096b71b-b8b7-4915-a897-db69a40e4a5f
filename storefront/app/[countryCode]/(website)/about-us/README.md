# About Us Page - Cokugoo Wood Carving

This directory contains the implementation of the About Us page for the Cokugoo wood carving e-commerce website.

## 📋 Requirements Compliance

This implementation strictly follows the SEO document requirements (`seo.md`) and includes:

### ✅ SEO Compliance
- **Title**: "Cokugoo: The Soul of Wood Carving Art & Hand Carved Wooden Animals"
- **Description**: Complete SEO-optimized meta description
- **Content**: All text content matches the SEO document exactly
- **Keywords**: Naturally integrated "wood carving art", "hand carved wooden animals", "wooden animal decor", "wooden animal figurines"

### ✅ Design Features
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Wood Carving Theme**: Warm, natural color palette with wood-inspired aesthetics
- **Bright Theme**: Light background with accent colors (#ff5227 orange, #fff6e6 cream)
- **Unique Design**: Custom layout different from reference site
- **No Gradients**: Clean typography without gradient text effects

### ✅ Content Sections

1. **Hero Section** (`hero-section.tsx`)
   - Main H1 title with exact SEO content
   - Introductory paragraph
   - Featured image placeholder

2. **Core Values** (`core-values.tsx`)
   - Three principle cards: Artisan Soul, Natural Harmony, Enduring Charm
   - Interactive hover effects
   - Icon-based visual design

3. **Meet Our Artisans** (`meet-artisans.tsx`)
   - Four artisan profiles: Time, Riley, Arlo, JIOFA
   - Individual specialties and descriptions
   - Image placeholders for future photos

4. **Cokugoo Belief** (`cokugoo-belief.tsx`)
   - Brand philosophy section
   - Founders' quote with proper attribution
   - Elegant quote styling

5. **Join Story** (`join-story.tsx`)
   - Call-to-action section
   - Link to product collection
   - Animated decorative elements

## 🎨 Design System

### Colors
- **Primary**: `#ff5227` (accent orange)
- **Background**: `#fff6e6` (warm cream)
- **Secondary**: `#f9dff2` (light pink)
- **Text**: Various opacity levels of accent color

### Typography
- **Headings**: Serif font family
- **Body**: Serif font family
- **Sizes**: Responsive scaling from mobile to desktop

### Components
- Modular component architecture
- Reusable design patterns
- Consistent spacing and layout

## 🖼️ Image Placeholders

All images are currently using placeholder designs with descriptive content:
- Hero section: Collection showcase
- Artisan photos: Individual portraits with workshop settings
- Decorative elements: SVG icons and geometric shapes

## 🧪 Testing

Basic test structure is included in `__tests__/about-us.test.tsx`:
- Component rendering verification
- Section presence validation
- SEO metadata structure (placeholder)

## 📱 Responsive Behavior

- **Mobile**: Single column layout, stacked sections
- **Tablet**: Two-column grids where appropriate
- **Desktop**: Full multi-column layouts with optimal spacing

## 🔗 Navigation

The page integrates with the existing site structure:
- Uses shared header/footer components
- Includes proper routing with country code support
- CTA button links to `/collections` page

## 🚀 Performance

- Optimized component structure
- Minimal external dependencies
- Efficient CSS with Tailwind utilities
- Proper semantic HTML structure

## 📝 Content Management

All content is hardcoded as requested, but the modular structure allows for easy future integration with:
- CMS systems
- Internationalization
- Dynamic content updates

## 🎯 SEO Optimization

- Proper heading hierarchy (H1, H2, H3)
- Semantic HTML structure
- Meta tags implementation
- Keyword-rich content placement
- Internal linking strategy
